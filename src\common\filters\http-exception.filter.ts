import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { DailyLogger } from '../loggers/daily-logger.service';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor(private readonly dailyLogger: DailyLogger) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    // 获取响应对象
    const response = ctx.getResponse<Response>();

    // 获取请求对象
    const request = ctx.getRequest<Request>();

    // 获取状态码
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    // 获取错误信息
    let message: string;
    let stack: string;
    let detail: any;

    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      message =
        typeof response === 'string' ? response : (response as any).message;
      stack = exception.stack;
      detail = response;
    } else if (exception instanceof Error) {
      message = exception.message;
      stack = exception.stack;
      detail = exception;
    } else {
      message = 'Internal server error';
      stack = '';
      detail = exception;
    }

    // 构建错误日志对象
    const errorLog = {
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      ip: request.ip,
      status,
      message,
      stack,
      detail,
      body: request.body,
      params: request.params,
      query: request.query,
      headers: request.headers,
      user: (request as any).user, // 如果有用户信息的话
    };

    // 记录错误日志
    if (status >= 500) {
      this.dailyLogger.error('Server Error', errorLog.message);
    } else {
      this.dailyLogger.warn(errorLog.message);
    }

    // 返回给客户端的响应
    const clientResponse = {
      success: false,
      statusCode: status,
      timestamp: errorLog.timestamp,
      path: request.url,
      message,
      // 在开发环境下可以返回更多调试信息
      ...(process.env.NODE_ENV === 'development' && {
        detail: detail,
        stack: stack,
      }),
    };

    response.status(status).json(clientResponse);
  }
}
