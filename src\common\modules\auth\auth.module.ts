import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './components/users/entities/user.entity';
import { Role } from './components/roles/entities/role.entity';
import { Permission } from './components/permissions/entities/permission.entity';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UsersModule } from './components/users/users.module';
import { RolesModule } from './components/roles/roles.module';
import { PermissionsModule } from './components/permissions/permissions.module';
import { CommonModule } from './components/common/common.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DeptsModule } from './components/depts/depts.module';
import { MenusModule } from './components/menus/menus.module';

@Module({
  imports: [
    CommonModule,
    UsersModule,
    RolesModule,
    PermissionsModule,
    DeptsModule,
    MenusModule,
  ],
  providers: [AuthService],
  controllers: [AuthController],
})
export class AuthModule {}
