import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * 定义统一的响应格式接口
 * @template T 响应数据的类型
 */
export interface Response<T> {
  success: boolean; // 请求是否成功
  data: T; // 返回的具体数据
}

/**
 * 响应拦截器
 * 用于统一处理所有成功的API响应格式
 * @template T 响应数据的类型
 */
@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  /**
   * 拦截器核心方法
   * @param context 执行上下文，包含请求相关信息
   * @param next 下一个处理程序
   * @returns 转换后的响应数据
   */
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    // 使用RxJS的map操作符转换响应数据
    return next.handle().pipe(
      map((data) => ({
        success: true, // 标记请求成功
        data: data || null, // 返回数据，如果为空则返回null
      })),
    );
  }
}
