import { Module } from '@nestjs/common';
import { OnlineLogsService } from './online-logs.service';
import { OnlineLogsController } from './online-logs.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OnlineLog } from './entities/online-log.entity';

@Module({
  imports: [TypeOrmModule.forFeature([OnlineLog])],
  controllers: [OnlineLogsController],
  providers: [OnlineLogsService],
  exports: [TypeOrmModule, OnlineLogsService], // 导出 TypeOrmModule 以便其他模块使用
})
export class OnlineLogsModule {}
