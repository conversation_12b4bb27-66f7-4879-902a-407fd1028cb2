import { Module } from '@nestjs/common';
import { DeptsService } from './depts.service';
import { DeptsController } from './depts.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Dept } from './entities/dept.entity';
import { RolesModule } from '../roles/roles.module';

@Module({
  imports: [TypeOrmModule.forFeature([Dept]), RolesModule],
  controllers: [DeptsController],
  providers: [DeptsService],
  exports: [TypeOrmModule, DeptsService], // 导出 TypeOrmModule 以便其他模块使用
})
export class DeptsModule {}
