import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request } from 'express';
import { UAParser } from 'ua-parser-js';
import * as geoip from 'geoip-lite';
import { OnlineLogsService } from '../modules/monitor/components/online-logs/online-logs.service';

/**
 * 用户信息拦截器
 * 用于在用户登录成功后记录用户信息
 */
@Injectable()
export class UserInfoInterceptor implements NestInterceptor {
  constructor(private readonly onlineLogsService: OnlineLogsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    // 只在登录成功后记录
    if (request.url.includes('/auth/login') && request.method === 'POST') {
      return next.handle().pipe(
        tap((data) => {
          if (data && data.username) {
            const userInfo = this.extractUserInfo(request, data);
            // Call the service (currently returns a string, not a Promise)
            this.onlineLogsService.create(userInfo);
          }
        }),
      );
    }

    return next.handle();
  }

  private extractUserInfo(request: Request, userData: any) {
    // 获取IP地址
    const ip = this.getIpAddress(request);

    // 解析User-Agent
    const userAgent = request.headers['user-agent'];
    const parser = new UAParser(userAgent);
    const browser = parser.getBrowser().name;
    const system = parser.getOS().name;

    // 获取地理位置
    const geo = geoip.lookup(ip);
    const address = geo ? `${geo.country}${geo.region}${geo.city}` : '未知位置';

    return {
      username: userData.username,
      ip,
      address,
      system,
      browser,
      loginTime: new Date(),
    };
  }

  private getIpAddress(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0];
    }

    return (
      request.socket.remoteAddress ||
      (request as any).connection?.remoteAddress ||
      '127.0.0.1'
    );
  }
}
