import {
  IsString,
  IsOptional,
  IsNumber,
  IsObject,
  IsEnum,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 监控类型枚举
 */
export enum MonitorType {
  SYSTEM = 'system', // 系统监控
  OPERATION = 'operation', // 操作监控
  LOGIN = 'login', // 登录监控
  REQUEST = 'request', // 请求监控
  ERROR = 'error', // 错误监控
  PERFORMANCE = 'performance', // 性能监控
}

/**
 * 监控状态枚举
 */
export enum MonitorStatus {
  SUCCESS = 1, // 成功
  FAILED = 0, // 失败
}

/**
 * 创建监控记录 DTO
 */
export class CreateMonitorDto {
  @ApiProperty({
    description: '监控类型',
    enum: MonitorType,
    example: MonitorType.SYSTEM,
  })
  @IsEnum(MonitorType)
  type: MonitorType;

  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  @IsString()
  username: string;

  @ApiProperty({
    description: 'IP地址',
    example: '*************',
  })
  @IsString()
  ip: string;

  @ApiPropertyOptional({
    description: '地理位置',
    example: '中国广东省深圳市',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: '操作系统',
    example: 'Windows 10',
  })
  @IsOptional()
  @IsString()
  system?: string;

  @ApiPropertyOptional({
    description: '浏览器类型',
    example: 'Chrome 120.0.0.0',
  })
  @IsOptional()
  @IsString()
  browser?: string;

  @ApiPropertyOptional({
    description: '所属模块',
    example: '用户管理',
  })
  @IsOptional()
  @IsString()
  module?: string;

  @ApiPropertyOptional({
    description: '操作概要/请求描述',
    example: '用户登录',
  })
  @IsOptional()
  @IsString()
  summary?: string;

  @ApiPropertyOptional({
    description: '请求方法',
    example: 'POST',
  })
  @IsOptional()
  @IsString()
  method?: string;

  @ApiPropertyOptional({
    description: '请求URL',
    example: '/api/auth/login',
  })
  @IsOptional()
  @IsString()
  url?: string;

  @ApiPropertyOptional({
    description: '请求耗时(毫秒)',
    example: 150,
  })
  @IsOptional()
  @IsNumber()
  takesTime?: number;

  @ApiProperty({
    description: '状态',
    enum: MonitorStatus,
    example: MonitorStatus.SUCCESS,
  })
  @IsEnum(MonitorStatus)
  status: MonitorStatus;

  @ApiPropertyOptional({
    description: '追踪ID',
    example: 'trace-123456789',
  })
  @IsOptional()
  @IsString()
  traceId?: string;

  @ApiPropertyOptional({
    description: '请求头信息',
  })
  @IsOptional()
  @IsObject()
  requestHeaders?: Record<string, any>;

  @ApiPropertyOptional({
    description: '请求体信息',
  })
  @IsOptional()
  @IsObject()
  requestBody?: Record<string, any>;

  @ApiPropertyOptional({
    description: '响应头信息',
  })
  @IsOptional()
  @IsObject()
  responseHeaders?: Record<string, any>;

  @ApiPropertyOptional({
    description: '响应体信息',
  })
  @IsOptional()
  @IsObject()
  responseBody?: Record<string, any>;

  @ApiPropertyOptional({
    description: '错误信息',
    example: '用户名或密码错误',
  })
  @IsOptional()
  @IsString()
  errorMessage?: string;

  @ApiPropertyOptional({
    description: '错误堆栈',
  })
  @IsOptional()
  @IsString()
  errorStack?: string;

  @ApiPropertyOptional({
    description: '用户代理字符串',
  })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({
    description: '备注信息',
  })
  @IsOptional()
  @IsString()
  remark?: string;
}
