import { IsString, IsOptional, IsDateString, IsNumber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 创建监控记录 DTO
 * 基于在线用户监控格式设计
 */
export class CreateOnlineLogDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  @IsString()
  username: string;

  @ApiProperty({
    description: 'IP地址',
    example: '*************',
  })
  @IsString()
  ip: string;

  @ApiProperty({
    description: '地理位置',
    example: '中国河南省信阳市',
  })
  @IsString()
  address: string;

  @ApiProperty({
    description: '操作系统',
    example: 'macOS',
  })
  @IsString()
  system: string;

  @ApiProperty({
    description: '浏览器类型',
    example: 'Chrome',
  })
  @IsString()
  browser: string;

  @ApiPropertyOptional({
    description: '登录时间',
    example: '2024-01-01T12:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  loginTime?: Date;

  @ApiProperty({
    description: '用户ID',
    example: 1,
  })
  @IsNumber()
  userId: number;
}
