import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1751789497917 implements MigrationInterface {
    name = 'Migration1751789497917'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "role_menu_relation" DROP CONSTRAINT "FK_f46d8eb5359af5f34998b018d82"`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" DROP CONSTRAINT "FK_b81d49ed8ca6f03f339c0e8e6b8"`);
        await queryRunner.query(`CREATE TABLE "online_logs" ("id" SERIAL NOT NULL, "username" character varying NOT NULL, "ip" character varying NOT NULL, "address" character varying NOT NULL, "system" character varying NOT NULL, "browser" character varying NOT NULL, "login_time" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c4284b5adc13bd1facea6fb5748" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" ADD CONSTRAINT "FK_f46d8eb5359af5f34998b018d82" FOREIGN KEY ("menu_id") REFERENCES "menu"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" ADD CONSTRAINT "FK_b81d49ed8ca6f03f339c0e8e6b8" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "role_menu_relation" DROP CONSTRAINT "FK_b81d49ed8ca6f03f339c0e8e6b8"`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" DROP CONSTRAINT "FK_f46d8eb5359af5f34998b018d82"`);
        await queryRunner.query(`DROP TABLE "online_logs"`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" ADD CONSTRAINT "FK_b81d49ed8ca6f03f339c0e8e6b8" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" ADD CONSTRAINT "FK_f46d8eb5359af5f34998b018d82" FOREIGN KEY ("menu_id") REFERENCES "menu"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
