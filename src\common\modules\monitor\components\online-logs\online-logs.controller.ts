import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { OnlineLogsService } from './online-logs.service';
import { CreateOnlineLogDto } from './dto/create-online-log.dto';
import { UpdateOnlineLogDto } from './dto/update-online-log.dto';

@Controller('online-logs')
export class OnlineLogsController {
  constructor(private readonly onlineLogsService: OnlineLogsService) {}

  @Post()
  create(@Body() createOnlineLogDto: CreateOnlineLogDto) {
    return this.onlineLogsService.create(createOnlineLogDto);
  }

  @Get()
  findAll() {
    return this.onlineLogsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.onlineLogsService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateOnlineLogDto: UpdateOnlineLogDto) {
    return this.onlineLogsService.update(+id, updateOnlineLogDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.onlineLogsService.remove(+id);
  }
}
