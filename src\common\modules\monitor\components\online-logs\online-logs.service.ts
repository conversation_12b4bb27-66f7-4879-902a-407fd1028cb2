import { Injectable } from '@nestjs/common';
import { CreateOnlineLogDto } from './dto/create-online-log.dto';
import { UpdateOnlineLogDto } from './dto/update-online-log.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, Like, Repository } from 'typeorm';
import { OnlineLog } from './entities/online-log.entity';

@Injectable()
export class OnlineLogsService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(OnlineLog)
    private readonly onlineLogRepository: Repository<OnlineLog>,
  ) {}

  async create(createOnlineLogDto: CreateOnlineLogDto) {
    // 先判断用户是否已经存在
    const online_log = await this.onlineLogRepository.findOne({
      where: { userId: createOnlineLogDto.userId },
    });
    if (online_log) {
      return this.onlineLogRepository.remove(online_log);
    }

    // 确定没有记录的时候才创建
    const online_log_info = this.onlineLogRepository.create(createOnlineLogDto);
    return this.onlineLogRepository.save(online_log_info);
  }

  findAll() {
    return `This action returns all onlineLogs`;
  }

  findOne(id: number) {
    return `This action returns a #${id} onlineLog`;
  }

  update(id: number, updateOnlineLogDto: UpdateOnlineLogDto) {
    return `This action updates a #${id} onlineLog`;
  }

  async remove(id: number) {
    // 先判断用户是否已经存在
    const online_log = await this.onlineLogRepository.findOne({
      where: { userId: id },
    });
    if (online_log) {
      return this.onlineLogRepository.remove(online_log);
    }
  }
}
