import { Injectable } from '@nestjs/common';
import { CreateOnlineLogDto } from './dto/create-online-log.dto';
import { UpdateOnlineLogDto } from './dto/update-online-log.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, Like, Repository } from 'typeorm';
import { OnlineLog } from './entities/online-log.entity';

@Injectable()
export class OnlineLogsService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(OnlineLog)
    private readonly onlineLogRepository: Repository<OnlineLog>,
  ) {}

  create(createOnlineLogDto: CreateOnlineLogDto) {
    const online_log = this.onlineLogRepository.create(createOnlineLogDto);
    return this.onlineLogRepository.save(online_log);
  }

  findAll() {
    return `This action returns all onlineLogs`;
  }

  findOne(id: number) {
    return `This action returns a #${id} onlineLog`;
  }

  update(id: number, updateOnlineLogDto: UpdateOnlineLogDto) {
    return `This action updates a #${id} onlineLog`;
  }

  remove(id: number) {
    return `This action removes a #${id} onlineLog`;
  }
}
