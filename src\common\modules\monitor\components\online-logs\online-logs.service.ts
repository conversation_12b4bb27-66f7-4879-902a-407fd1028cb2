import { Injectable } from '@nestjs/common';
import { CreateOnlineLogDto } from './dto/create-online-log.dto';
import { UpdateOnlineLogDto } from './dto/update-online-log.dto';

@Injectable()
export class OnlineLogsService {
  create(createOnlineLogDto: CreateOnlineLogDto) {
    return 'This action adds a new onlineLog';
  }

  findAll() {
    return `This action returns all onlineLogs`;
  }

  findOne(id: number) {
    return `This action returns a #${id} onlineLog`;
  }

  update(id: number, updateOnlineLogDto: UpdateOnlineLogDto) {
    return `This action updates a #${id} onlineLog`;
  }

  remove(id: number) {
    return `This action removes a #${id} onlineLog`;
  }
}
