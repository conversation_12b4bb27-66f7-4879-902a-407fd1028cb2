import {
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Role } from '../../roles/entities/role.entity';

export enum DeptState {
  Female = 0,
  Male = 1,
}

@Entity()
export class Dept {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string; // 部门名称

  @Column({
    nullable: true,
    length: 50,
  })
  nickname: string; // 昵称

  @Column({ nullable: true })
  parentId: number; // 父部门id

  @Column()
  sort: number; // 排序

  @Column({ nullable: true })
  principal: string; // 负责人

  @Column({ nullable: true })
  phone: string; // 负责人手机

  @Column({ nullable: true })
  email: string; // 负责人邮箱

  @Column({ type: 'enum', enum: [0, 1] })
  status: 0 | 1; // 状态 1 启用 0 停用

  @Column({ nullable: true })
  remark: string; // 备注

  @ManyToOne(() => Dept, (dept) => dept.children)
  parent: Dept; // 父部门

  @OneToMany(() => Dept, (dept) => dept.parent)
  children: Dept[]; // 子部门

  @OneToMany(() => Role, (role) => role.dept)
  roles: Role[]; // 角色
}
