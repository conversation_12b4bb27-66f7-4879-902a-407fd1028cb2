import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import { resolve } from 'path';
import { log } from 'console';

/** 数据库迁移操作
 * 1. 创建数据库迁移文件 pnpm run migration:generate
 * 2. 执行迁移操作 pnpm run migration:run
 * 3. 回滚迁移操作 pnpm run migration:revert
 */

const envPath = resolve(
  __dirname,
  `env/.env.${process.env.NODE_ENV || 'development'}`,
);

// 根据 NODE_ENV 加载对应的 .env 文件
config({ path: envPath });

const configService = new ConfigService();

const AppDataSource = new DataSource({
  type: 'postgres',
  host: configService.get<string>('DATABASE_HOST'),
  port: parseInt(configService.get<string>('DATABASE_PORT'), 5432),
  username: configService.get<string>('DATABASE_USER'),
  password: configService.get<string>('DATABASE_PASSWORD'),
  database: configService.get<string>('DATABASE_NAME'),
  synchronize: false,
  entities: ['**/*.entity.ts'],
  migrations: ['src/migrations/*-migration.ts'],
  migrationsRun: true,
  logging: true,
});

export default AppDataSource;
