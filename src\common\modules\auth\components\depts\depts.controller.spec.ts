import { Test, TestingModule } from '@nestjs/testing';
import { DeptsController } from './depts.controller';
import { DeptsService } from './depts.service';

describe('DeptsController', () => {
  let controller: DeptsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DeptsController],
      providers: [DeptsService],
    }).compile();

    controller = module.get<DeptsController>(DeptsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
