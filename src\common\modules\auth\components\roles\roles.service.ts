import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { UpdateRoleDto } from './dto/update-role.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { Role } from './entities/role.entity';
import { EntityManager, In, Or, Repository } from 'typeorm';
import { CreatePermissionDto } from '../permissions/dto/create-permission.dto';
import { CreateRoleDto, SearchRoleDto } from './dto/create-role.dto';
import { Menu } from '../menus/entities/menu.entity';

@Injectable()
export class RolesService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  /**
   * 创建角色
   * @param createRoleDto
   * @returns
   */
  async create(createRoleDto: CreateRoleDto): Promise<Role> {
    const role = this.roleRepository.create(createRoleDto);
    return await this.roleRepository.save(role);
  }

  /**
   * 查询所有角色
   * @returns
   */
  async findAll(): Promise<Role[]> {
    return this.roleRepository.find();
  }

  /**
   * 根据条件查询用户
   */
  async findByCondition(
    searchRoleDto: SearchRoleDto,
    page: number = 1,
    limit: number = 10,
  ) {
    const { name, code, ...otherConditions } = searchRoleDto;

    const queryBuilder = this.entityManager
      .createQueryBuilder(Role, 'role')
      .where(otherConditions);

    if (name) {
      queryBuilder.andWhere('role.name LIKE :name', {
        name: `%${name}%`,
      });
    }

    if (code) {
      queryBuilder.andWhere('role.code LIKE :code', {
        code: `%${code}%`,
      });
    }

    queryBuilder.skip((page - 1) * limit).take(limit);

    const [roles, total] = await queryBuilder.getManyAndCount();

    return {
      list: roles,
      total,
      currentPage: page,
      pageSize: limit,
    };
  }

  /**
   * 根据角色 id 查询角色信息
   * @param roleIds
   * @returns
   */
  async findRolesByIds(roleIds: number[]): Promise<Role[]> {
    return this.entityManager.find(Role, {
      where: {
        id: In(roleIds),
      },
      relations: {
        permissions: true,
        menus: true,
      },
    });
  }

  /**
   * 查询单个角色
   * @param id
   * @returns
   */
  async findOne(id: number) {
    const role = await this.roleRepository.findOne({
      where: { id },
    });
    if (!role) {
      throw new NotFoundException(`没有找到ID为 ${id} 的角色`);
    }
    return role;
  }

  /**
   * 修改角色
   * @param id
   * @param updateRoleDto
   * @returns
   */
  async update(id: number, updateRoleDto: UpdateRoleDto) {
    const role = await this.roleRepository.preload({
      id: id,
      ...updateRoleDto,
    });
    if (!role) {
      throw new NotFoundException(`没有找到ID为 ${id} 的角色`);
    }
    return this.roleRepository.save(role);
  }

  /**
   * 删除角色
   * @param id
   */
  async remove(id: number): Promise<void> {
    const role = await this.findOne(id);
    await this.roleRepository.remove(role);
  }

  /**
   * 批量删除角色
   * @param ids
   */
  async delete(ids: number[]) {
    const roles = await this.roleRepository.find({
      where: { id: In(ids) },
    });
    await this.roleRepository.remove(roles);
  }

  /**
   * 更新角色菜单权限
   * @param roleId
   * @param menuIds
   */
  async updateRoleMenu(roleId: number, menuIds: number[]): Promise<any> {
    // 在这里实现更新角色菜单权限的逻辑
    // 例如：找到角色并更新其菜单
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['menus'],
    });

    if (!role) {
      throw new NotFoundException(`没有找到ID为 ${roleId} 的角色`);
    }

    // 根据菜单id查询菜单
    const menus = await this.entityManager.find(Menu, {
      where: { id: In(menuIds) },
    });

    // 更新角色的菜单
    role.menus = menus;

    return this.roleRepository.save(role);
  }
}
