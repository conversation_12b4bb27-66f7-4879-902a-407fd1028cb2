import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

/** 权限命名规则
 * 1. 目前权限使用“ *:*:* ” 的形式，
 * 2. 如后端权限为“ backend:user:add ”，则网页端权限为“ web:user:btn:add ”
 * 3. 也可以定义通用权限，如“ permission:btn:add ”
 */
@Entity()
export class Permission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    length: 50,
  })
  name: string;

  @Column({
    length: 100,
    nullable: true,
  })
  desc: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}
