import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { Role } from '../components/roles/entities/role.entity';
import { Reflector } from '@nestjs/core';

declare module 'express' {
  interface Request {
    user: {
      username: string;
      roles: Role[];
    };
  }
}

@Injectable()
export class LoginGuard implements CanActivate {
  @Inject(JwtService)
  private jwtService: JwtService;

  @Inject('Reflector')
  private reflector: Reflector;

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // 获取当前请求是否需要登录，判断装饰器是否存在
    const notRequireLogin = this.reflector.getAllAndOverride(
      'not-require-login',
      [context.getClass(), context.getHandler()],
    );

    if (notRequireLogin) {
      return true;
    }

    const request: Request = context.switchToHttp().getRequest();

    const authorization = request.headers.authorization;

    // 判断是否有 token
    if (!authorization) {
      throw new UnauthorizedException('用户未登录');
    }

    // TODO：这里后续可以增加登录限制等操作

    try {
      // 解析 token 获取用户信息
      const token = authorization.split(' ')[1];
      const data = this.jwtService.verify(token);
      request.user = data.user;
      return true;
    } catch (e) {
      throw new UnauthorizedException('token 失效，请重新登录');
    }
  }
}
