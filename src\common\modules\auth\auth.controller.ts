import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { User } from './components/users/entities/user.entity';
import { SignInDto } from './dto/login.dto';
import { NotRequireLogin } from './components/common/decorators/not-require-login.decorator';
import { LoginUserDto } from './components/users/dto/create-user.dto';
import { UsersService } from './components/users/users.service';
import { JwtService } from '@nestjs/jwt';

@ApiTags('auth(权限认证公共)') // 为控制器添加标签
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  @Get('init')
  @ApiOperation({ summary: '初始化数据' })
  @NotRequireLogin()
  async initData() {
    // 初始化数据
    await this.authService.initData();
    return 'done';
  }

  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @NotRequireLogin() // 表示这个接口不需要登录
  @ApiBody({
    description: '用户登录',
    type: LoginUserDto,
    examples: {
      example1: {
        summary: '测试数据',
        description: '测试账号',
        value: {
          username: 'test',
          password: 'test123',
        },
      },
    },
  })
  async login(@Body() loginUser: LoginUserDto) {
    const user = await this.usersService.login(loginUser);

    // 生成 token
    const accessToken = this.jwtService.sign({
      user: {
        username: user.username,
        roles: user.roles,
      },
    });

    // 生成刷新token
    const refreshToken = this.jwtService.sign(
      {
        user: {
          username: user.username,
          roles: user.roles,
        },
      },
      {
        expiresIn: '7d', // 设置刷新 token 的过期时间
      },
    );

    return {
      avatar: 'https://avatars.githubusercontent.com/u/44761321',
      username: user.username,
      nickname: user.nickname,
      // 一个用户可能有多个角色
      roles: user.roles.map((role) => role.code),
      // 按钮级别权限
      permissions: user.roles
        .map((role) => role.permissions.map((p) => p.name))
        .flat(),
      accessToken: accessToken,
      refreshToken: refreshToken,
      // 过期时间
      expires: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
    };
  }
}
