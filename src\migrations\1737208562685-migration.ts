import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1737208562685 implements MigrationInterface {
    name = 'Migration1737208562685'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "permission" ("id" SERIAL NOT NULL, "name" character varying(50) NOT NULL, "desc" character varying(100), "createTime" TIMESTAMP NOT NULL DEFAULT now(), "updateTime" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "menu" ("id" SERIAL NOT NULL, "menuType" integer NOT NULL DEFAULT '0', "title" character varying(100) NOT NULL, "name" character varying(100) NOT NULL, "path" character varying(200) NOT NULL, "component" character varying(200), "rank" integer, "redirect" character varying(200), "icon" character varying(100), "extraIcon" character varying(100), "enterTransition" character varying(100), "leaveTransition" character varying(100), "activePath" character varying(200), "auths" character varying(200), "frameSrc" character varying(200), "frameLoading" boolean NOT NULL DEFAULT true, "keepAlive" boolean NOT NULL DEFAULT false, "hiddenTag" boolean NOT NULL DEFAULT false, "fixedTag" boolean NOT NULL DEFAULT false, "showLink" boolean NOT NULL DEFAULT true, "showParent" boolean NOT NULL DEFAULT false, "createTime" TIMESTAMP NOT NULL DEFAULT now(), "updateTime" TIMESTAMP NOT NULL DEFAULT now(), "parentId" integer, CONSTRAINT "PK_35b2a8f47d153ff7a41860cceeb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role" ("id" SERIAL NOT NULL, "code" character varying(20) NOT NULL, "name" character varying(50) NOT NULL, "remark" character varying(255), "status" "public"."role_status_enum" NOT NULL, "createTime" TIMESTAMP NOT NULL DEFAULT now(), "updateTime" TIMESTAMP NOT NULL DEFAULT now(), "deptId" integer, CONSTRAINT "UQ_ee999bb389d7ac0fd967172c41f" UNIQUE ("code"), CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "dept" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "nickname" character varying(50), "parentId" integer, "sort" integer NOT NULL, "principal" character varying, "phone" character varying, "email" character varying, "status" "public"."dept_status_enum" NOT NULL, "remark" character varying, CONSTRAINT "UQ_5eb4a4c9f25934f105299edffd7" UNIQUE ("name"), CONSTRAINT "PK_deff0441db275143073fd33362a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user" ("id" SERIAL NOT NULL, "username" character varying NOT NULL, "password" character varying(50) NOT NULL, "nickname" character varying NOT NULL, "avatar" character varying, "email" character varying, "status" "public"."user_status_enum" NOT NULL, "phone" character varying(11), "sex" "public"."sex" NOT NULL, "remark" character varying, "createTime" TIMESTAMP NOT NULL DEFAULT now(), "updateTime" TIMESTAMP NOT NULL DEFAULT now(), "deptId" integer, CONSTRAINT "UQ_78a916df40e02a9deb1c4b75edb" UNIQUE ("username"), CONSTRAINT "UQ_e2364281027b926b879fa2fa1e0" UNIQUE ("nickname"), CONSTRAINT "UQ_8e1f623798118e629b46a9e6299" UNIQUE ("phone"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role_menu_relation" ("menu_id" integer NOT NULL, "role_id" integer NOT NULL, CONSTRAINT "PK_ec6fcf4d7ec4abc4dc1b6c60188" PRIMARY KEY ("menu_id", "role_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f46d8eb5359af5f34998b018d8" ON "role_menu_relation" ("menu_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_b81d49ed8ca6f03f339c0e8e6b" ON "role_menu_relation" ("role_id") `);
        await queryRunner.query(`CREATE TABLE "role_permission_relation" ("roleId" integer NOT NULL, "permissionId" integer NOT NULL, CONSTRAINT "PK_8d38235c0e5f1a418cec488d721" PRIMARY KEY ("roleId", "permissionId"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e1ca88973e6058882146e25401" ON "role_permission_relation" ("roleId") `);
        await queryRunner.query(`CREATE INDEX "IDX_7822b319e3e15d982d49aa50cf" ON "role_permission_relation" ("permissionId") `);
        await queryRunner.query(`CREATE TABLE "user_role_relation" ("userId" integer NOT NULL, "roleId" integer NOT NULL, CONSTRAINT "PK_2d4d2b8a411a61810168e76799e" PRIMARY KEY ("userId", "roleId"))`);
        await queryRunner.query(`CREATE INDEX "IDX_387a09a362c32ee04b33fc4eaa" ON "user_role_relation" ("userId") `);
        await queryRunner.query(`CREATE INDEX "IDX_bed18db98a78c46f0bcfedfe65" ON "user_role_relation" ("roleId") `);
        await queryRunner.query(`ALTER TABLE "menu" ADD CONSTRAINT "FK_23ac1b81a7bfb85b14e86bd23a5" FOREIGN KEY ("parentId") REFERENCES "menu"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role" ADD CONSTRAINT "FK_46516118d5bf0bf3616f9a6b776" FOREIGN KEY ("deptId") REFERENCES "dept"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "dept" ADD CONSTRAINT "FK_889b72920d0e1dcb01aedbf2976" FOREIGN KEY ("parentId") REFERENCES "dept"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_b79e66a3f148e12f9eb5dafb3c0" FOREIGN KEY ("deptId") REFERENCES "dept"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" ADD CONSTRAINT "FK_f46d8eb5359af5f34998b018d82" FOREIGN KEY ("menu_id") REFERENCES "menu"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" ADD CONSTRAINT "FK_b81d49ed8ca6f03f339c0e8e6b8" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_permission_relation" ADD CONSTRAINT "FK_e1ca88973e6058882146e254018" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "role_permission_relation" ADD CONSTRAINT "FK_7822b319e3e15d982d49aa50cf2" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "user_role_relation" ADD CONSTRAINT "FK_387a09a362c32ee04b33fc4eaab" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "user_role_relation" ADD CONSTRAINT "FK_bed18db98a78c46f0bcfedfe652" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_role_relation" DROP CONSTRAINT "FK_bed18db98a78c46f0bcfedfe652"`);
        await queryRunner.query(`ALTER TABLE "user_role_relation" DROP CONSTRAINT "FK_387a09a362c32ee04b33fc4eaab"`);
        await queryRunner.query(`ALTER TABLE "role_permission_relation" DROP CONSTRAINT "FK_7822b319e3e15d982d49aa50cf2"`);
        await queryRunner.query(`ALTER TABLE "role_permission_relation" DROP CONSTRAINT "FK_e1ca88973e6058882146e254018"`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" DROP CONSTRAINT "FK_b81d49ed8ca6f03f339c0e8e6b8"`);
        await queryRunner.query(`ALTER TABLE "role_menu_relation" DROP CONSTRAINT "FK_f46d8eb5359af5f34998b018d82"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_b79e66a3f148e12f9eb5dafb3c0"`);
        await queryRunner.query(`ALTER TABLE "dept" DROP CONSTRAINT "FK_889b72920d0e1dcb01aedbf2976"`);
        await queryRunner.query(`ALTER TABLE "role" DROP CONSTRAINT "FK_46516118d5bf0bf3616f9a6b776"`);
        await queryRunner.query(`ALTER TABLE "menu" DROP CONSTRAINT "FK_23ac1b81a7bfb85b14e86bd23a5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bed18db98a78c46f0bcfedfe65"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_387a09a362c32ee04b33fc4eaa"`);
        await queryRunner.query(`DROP TABLE "user_role_relation"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7822b319e3e15d982d49aa50cf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e1ca88973e6058882146e25401"`);
        await queryRunner.query(`DROP TABLE "role_permission_relation"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b81d49ed8ca6f03f339c0e8e6b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f46d8eb5359af5f34998b018d8"`);
        await queryRunner.query(`DROP TABLE "role_menu_relation"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP TABLE "dept"`);
        await queryRunner.query(`DROP TABLE "role"`);
        await queryRunner.query(`DROP TABLE "menu"`);
        await queryRunner.query(`DROP TABLE "permission"`);
    }

}
