import { Injectable } from '@nestjs/common';
import { CreateDeptDto, SearchDeptDto } from './dto/create-dept.dto';
import { UpdateDeptDto } from './dto/update-dept.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { Dept } from './entities/dept.entity';
import { EntityManager, Repository } from 'typeorm';

@Injectable()
export class DeptsService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(Dept)
    private readonly deptRepository: Repository<Dept>,
  ) {}

  /**
   * 创建部门
   * @param createDeptDto
   * @returns
   */
  create(createDeptDto: CreateDeptDto) {
    // 剔除 higherMenuOptions
    const { higherDeptOptions, ...otherUpdateDeptDto } = createDeptDto;

    const dept = this.deptRepository.create(otherUpdateDeptDto);
    return this.deptRepository.save(dept);
  }

  /**
   * 查询所有用户
   * @returns
   */
  async findAll() {
    return this.deptRepository.find({
      relations: ['roles'],
    });
  }

  /**
   * 根据条件查询部门
   */
  async findByCondition(
    searchDeptDto: SearchDeptDto,
    page: number = 1,
    limit: number = 10,
  ) {
    const [depts, total] = await this.entityManager.findAndCount(Dept, {
      where: searchDeptDto,
      relations: ['roles'],
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      list: depts,
      total,
      currentPage: page,
      pageSize: limit,
    };
  }

  async update(id: number, updateDeptDto: UpdateDeptDto) {
    // 剔除 higherMenuOptions
    const { higherDeptOptions, ...otherUpdateDeptDto } = updateDeptDto;

    return this.deptRepository.update(id, otherUpdateDeptDto);
  }

  remove(id: number) {
    return `This action removes a #${id} dept`;
  }
}
