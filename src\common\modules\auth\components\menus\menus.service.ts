import { Injectable } from '@nestjs/common';
import { CreateMenuDto, SearchMenuDto } from './dto/create-menu.dto';
import { UpdateMenuDto } from './dto/update-menu.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Menu } from './entities/menu.entity';
import { convertTheDataFormat } from './utils/helper';

@Injectable()
export class MenusService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(Menu)
    private readonly menuRepository: Repository<Menu>,
  ) {}
  async create(createMenuDto: CreateMenuDto) {
    // 剔除 higherMenuOptions
    const { parentId, higherMenuOptions, ...otherUpdateMenuDto } =
      createMenuDto;

    // 查询 parent
    const parent = await this.menuRepository.findOne({
      where: { id: parentId },
    });

    const createMenu = {
      ...otherUpdateMenuDto,
      rank: otherUpdateMenuDto.rank,
      parent: parent,
    };

    const menu = this.menuRepository.create(createMenu);
    return this.menuRepository.save(menu);
  }

  /**
   * 批量删除
   */
  async delete(ids: number[]) {
    return this.menuRepository.delete(ids);
  }

  /**
   * 异步获取菜单路由
   */
  async getAsyncRoutes() {
    const menus = await this.menuRepository.find({
      relations: ['parent', 'roles'],
    });

    // 转换数据格式
    const out_menus = convertTheDataFormat(menus);

    return out_menus;
  }

  /**
   * 根据条件查询用户
   */
  async findByCondition(
    searchMenuDto: SearchMenuDto,
    page: number = 1,
    limit: number = 10,
  ) {
    const { ...otherConditions } = searchMenuDto;

    const queryBuilder = this.entityManager
      .createQueryBuilder(Menu, 'menu')
      .leftJoinAndSelect('menu.parent', 'parent')
      .where(otherConditions);

    queryBuilder.skip((page - 1) * limit).take(limit);

    const [menus, total] = await queryBuilder.getManyAndCount();

    return menus.map((menu) => {
      return {
        ...menu,
        parentId: menu.parent?.id ?? 0,
      };
    });
  }

  findAll() {
    return this.menuRepository.find({
      relations: ['parent'],
    });
  }

  findOne(id: number) {
    return this.menuRepository.findOne({
      where: { id },
      relations: ['parent'],
    });
  }

  async update(id: number, updateMenuDto: UpdateMenuDto) {
    // 剔除 higherMenuOptions
    const { parentId, higherMenuOptions, ...otherUpdateMenuDto } =
      updateMenuDto;

    // 查询 parent
    const parent = await this.menuRepository.findOne({
      where: { id: parentId },
    });

    const updateMenu = {
      ...otherUpdateMenuDto,
      parent: parent,
      rank: otherUpdateMenuDto.rank ?? 0, // 确保rank有默认值
    };

    return this.menuRepository.update(id, updateMenu);
  }

  remove(id: number) {
    return this.menuRepository.delete(id);
  }
}
