import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { User } from './components/users/entities/user.entity';
import { Role } from './components/roles/entities/role.entity';
import { Permission } from './components/permissions/entities/permission.entity';
import { UsersService } from './components/users/users.service';
import { JwtService } from '@nestjs/jwt';
import { Dept } from './components/depts/entities/dept.entity';
import { Menu } from './components/menus/entities/menu.entity';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {}

  // 配置初始化数据
  async initData() {
    const userCount = await this.entityManager.count(User);
    if (userCount > 0) {
      return; // 数据已经存在，不需要初始化
    }

    // 创建权限
    const permissions = [
      { name: '*:*:*' }, // 所有权限
      { name: 'permission:btn:add' },
      { name: 'permission:btn:edit' },
      { name: 'permission:btn:delete' },
    ].map((data) => {
      const permission = new Permission();
      permission.name = data.name;
      return permission;
    });

    const mainDept = new Dept();
    mainDept.nickname = '平台';
    mainDept.name = 'platform';
    mainDept.sort = 1;
    mainDept.status = 1;

    const subDept = new Dept();
    subDept.nickname = '技术部';
    subDept.name = 'platform-technical';
    subDept.sort = 1;
    subDept.status = 1;
    subDept.parentId = 1;

    // 创建系统管理父级菜单
    const systemMenu = new Menu();
    systemMenu.menuType = 0; // 目录
    systemMenu.title = '系统管理';
    systemMenu.name = 'System';
    systemMenu.path = '/system';
    systemMenu.rank = 1;
    systemMenu.icon = 'ri:settings-3-line';

    // 创建用户管理子菜单
    const userMenu = new Menu();
    userMenu.menuType = 0; // 菜单
    userMenu.title = '用户管理';
    userMenu.name = 'SystemUser';
    userMenu.path = '/system/user/index';
    userMenu.icon = 'ri:admin-line';
    userMenu.parent = systemMenu;
    userMenu.rank = 0;

    // 创建角色管理子菜单
    const roleMenu = new Menu();
    roleMenu.menuType = 0;
    roleMenu.title = '角色管理';
    roleMenu.name = 'SystemRole';
    roleMenu.path = '/system/role/index';
    roleMenu.icon = 'ri:admin-fill';
    roleMenu.parent = systemMenu;
    roleMenu.rank = 0;

    // 创建菜单管理子菜单
    const menuManageMenu = new Menu();
    menuManageMenu.menuType = 0;
    menuManageMenu.title = '菜单管理';
    menuManageMenu.name = 'SystemMenu';
    menuManageMenu.path = '/system/menu/index';
    menuManageMenu.icon = 'ep:menu';
    menuManageMenu.parent = systemMenu;
    menuManageMenu.rank = 0;

    // 创建部门管理子菜单
    const deptMenu = new Menu();
    deptMenu.menuType = 0;
    deptMenu.title = '部门管理';
    deptMenu.name = 'SystemDept';
    deptMenu.path = '/system/dept/index';
    deptMenu.icon = 'ri:git-branch-line';
    deptMenu.parent = systemMenu;
    deptMenu.rank = 0;

    // 创建系统监控父级菜单
    const monitorMenu = new Menu();
    monitorMenu.menuType = 0; // 目录
    monitorMenu.title = '系统监控';
    monitorMenu.name = 'Monitor';
    monitorMenu.path = '/monitor';
    monitorMenu.rank = 1;
    monitorMenu.icon = 'ep:monitor';

    // 创建在线用户子菜单
    const onlineUserMenu = new Menu();
    onlineUserMenu.menuType = 0;
    onlineUserMenu.title = '在线用户';
    onlineUserMenu.name = 'OnlineUser';
    onlineUserMenu.path = '/monitor/online/index';
    onlineUserMenu.icon = 'ri:git-branch-line';
    onlineUserMenu.parent = monitorMenu;
    onlineUserMenu.rank = 0;

    // 创建登录日志子菜单
    const loginLogsMenu = new Menu();
    loginLogsMenu.menuType = 0;
    loginLogsMenu.title = '登录日志';
    loginLogsMenu.name = 'LoginLogs';
    loginLogsMenu.path = '/monitor/logs/login/index';
    loginLogsMenu.icon = 'ri:git-branch-line';
    loginLogsMenu.parent = monitorMenu;
    loginLogsMenu.rank = 0;

    // 创建操作日志子菜单
    const operationLogsMenu = new Menu();
    operationLogsMenu.menuType = 0;
    operationLogsMenu.title = '操作日志';
    operationLogsMenu.name = 'OperationLogs';
    operationLogsMenu.path = '/monitor/logs/operation/index';
    operationLogsMenu.icon = 'ri:git-branch-line';
    operationLogsMenu.parent = monitorMenu;
    operationLogsMenu.rank = 0;

    // 创建系统日志子菜单
    const systemLogsMenu = new Menu();
    systemLogsMenu.menuType = 0;
    systemLogsMenu.title = '系统日志';
    systemLogsMenu.name = 'SystemLogs';
    systemLogsMenu.path = '/monitor/logs/system/index';
    systemLogsMenu.icon = 'ri:git-branch-line';
    systemLogsMenu.parent = monitorMenu;
    systemLogsMenu.rank = 0;

    // 保存所有菜单
    const menus = [
      systemMenu,
      userMenu,
      roleMenu,
      menuManageMenu,
      deptMenu,
      monitorMenu,
      onlineUserMenu,
      loginLogsMenu,
      operationLogsMenu,
      systemLogsMenu,
    ];

    // 创建角色
    const roleAdmin = new Role();
    roleAdmin.code = 'admin';
    roleAdmin.name = '管理员';
    roleAdmin.permissions = permissions.slice(0, 1);
    roleAdmin.menus = menus; // 为管理员角色分配所有菜单
    roleAdmin.status = 1;
    roleAdmin.remark = '';

    const roleUser = new Role();
    roleUser.code = 'guest';
    roleUser.name = '游客';
    roleUser.permissions = permissions.slice(1, 4);
    roleUser.menus = [systemMenu, userMenu]; // 为游客角色只分配系统管理和用户管理菜单
    roleUser.status = 1;
    roleUser.remark = '';

    // 创建用户
    const userAdmin = new User();
    userAdmin.username = 'admin';
    userAdmin.password = 'admin123';
    userAdmin.nickname = '管理员';
    userAdmin.phone = '13800138111';
    userAdmin.sex = 1;
    userAdmin.status = 1;
    userAdmin.roles = [roleAdmin];
    userAdmin.dept = mainDept;

    const userJohn = new User();
    userJohn.username = 'test';
    userJohn.password = 'test123';
    userJohn.nickname = '测试用户';
    userJohn.phone = '13800138000';
    userJohn.sex = 0;
    userJohn.status = 1;
    userJohn.roles = [roleUser];
    userJohn.dept = subDept;

    // 保存数据
    await this.entityManager.save(Dept, [mainDept, subDept]);
    await this.entityManager.save(Permission, permissions);
    await this.entityManager.save(Menu, menus);
    await this.entityManager.save(Role, [roleAdmin, roleUser]);
    await this.entityManager.save(User, [userAdmin, userJohn]);
  }

  async signIn(username: string, pass: string): Promise<any> {
    // 验证账号密码是否正确
    const user = await this.userRepository.findOne({ where: { username } });
    if (user?.password !== pass) {
      throw new UnauthorizedException();
    }

    // 通过用户名和用户 ID 生成 JWT
    const payload = { sub: user.id, username: user.username };
    return {
      access_token: await this.jwtService.signAsync(payload),
    };
  }
}
