import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from './common/modules/auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { LoginGuard } from './common/modules/auth/guards/login.guard';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { PermissionGuard } from './common/modules/auth/guards/permission.guard';
import { UsersModule } from './common/modules/auth/components/users/users.module';
import { UsersService } from './common/modules/auth/components/users/users.service';
import { DailyLogger } from './common/loggers/daily-logger.service';
import { RequestLoggerMiddleware } from './common/middlewares/request-logger.middleware';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { UserInfoInterceptor } from './common/interceptors/user-info.interceptor';
import { MonitorModule } from './common/modules/monitor/monitor.module';

@Module({
  imports: [
    JwtModule.register({
      global: true,
      secret: 'water',
      signOptions: {
        expiresIn: '1d',
      },
    }),
    ConfigModule.forRoot({
      envFilePath: `src/config/env/.env.${
        process.env.NODE_ENV || 'development'
      }`,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DATABASE_HOST'),
        port: configService.get<number>('DATABASE_PORT'),
        username: configService.get<string>('DATABASE_USER'),
        password: configService.get<string>('DATABASE_PASSWORD'),
        database: configService.get<string>('DATABASE_NAME'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: false,
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    AuthModule,
    MonitorModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD, // 全局守卫, 用于权限控制
      useClass: LoginGuard,
    },
    {
      provide: APP_INTERCEPTOR, // 全局拦截器, 用于统一处理返回结果
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_FILTER, // 全局异常过滤器, 用于统一处理异常
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR, // 登录日志拦截器，用于记录用户登录信息
      useClass: UserInfoInterceptor,
    },
    DailyLogger,
    AppService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes('*');
  }
}
