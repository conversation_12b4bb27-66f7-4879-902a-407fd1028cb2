# NestJS 企业级脚手架

<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

<p align="center">
  基于 NestJS 构建的企业级后端脚手架，集成完整的用户认证授权系统、数据库管理、日志记录等功能
</p>

<p align="center">
  <a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
  <a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
  <img src="https://img.shields.io/badge/node-%3E%3D16-brightgreen.svg" alt="Node Version" />
  <img src="https://img.shields.io/badge/typescript-%5E5.1.3-blue.svg" alt="TypeScript Version" />
</p>

## ✨ 特性

- 🚀 **现代化技术栈**: NestJS 10 + TypeScript 5 + PostgreSQL
- 🔐 **完整认证系统**: JWT + RBAC 权限控制
- 📊 **API 文档**: 集成 Swagger/OpenAPI 自动生成文档
- 🗄️ **数据库管理**: TypeORM + 数据库迁移
- 📝 **日志系统**: Winston 日志记录 + 按日轮转
- 🛡️ **安全防护**: 全局异常处理 + 请求验证
- 🧪 **测试支持**: Jest 单元测试 + E2E 测试
- 🔧 **开发体验**: 热重载 + ESLint + Prettier
- 📦 **生产就绪**: 环境配置 + 容器化支持

## 📋 系统要求

- Node.js >= 16.0.0
- PostgreSQL >= 12.0
- pnpm >= 7.0.0 (推荐)

## 🏗️ 技术栈

### 核心框架

- **NestJS 10.x**: 基于 Node.js 的渐进式框架，采用装饰器和依赖注入模式
- **TypeScript 5.x**: 提供强类型支持和现代 JavaScript 特性
- **Express**: 作为底层 HTTP 服务器

### 数据库相关

- **TypeORM 0.3.x**: 强大的 ORM 框架，支持数据库迁移
- **PostgreSQL**: 主数据库，通过 `pg` 驱动连接

### 认证授权

- **JWT**: 基于 JSON Web Token 的身份认证
- **RBAC**: 基于角色的访问控制系统

### 开发工具

- **Winston**: 企业级日志记录
- **Swagger/OpenAPI**: 自动生成 API 文档
- **Jest**: 测试框架
- **ESLint + Prettier**: 代码质量和格式化

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd nestjs-scaffold
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境配置

在 `src/config/env/` 目录下创建环境配置文件：

```bash
# 开发环境配置
touch src/config/env/.env.development
```

编辑 `.env.development` 文件，添加以下配置：

```env
# 应用配置
NODE_ENV=development
PORT=3000

# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=your_password
DATABASE_NAME=nestjs_scaffold

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1d

# 日志配置
LOG_LEVEL=debug
```

### 4. 数据库设置

```bash
# 创建数据库
createdb nestjs_scaffold

# 运行数据库迁移
pnpm run migration:run
```

### 5. 启动应用

```bash
# 开发模式（热重载）
pnpm run dev

# 普通启动
pnpm run start

# 生产模式
pnpm run build
pnpm run prod
```

应用启动后，访问：

- API 服务: http://localhost:3000
- API 文档: http://localhost:3000/api

## 📁 项目结构

```text
src/
├── common/                     # 公共模块
│   ├── decorators/            # 自定义装饰器
│   ├── filters/               # 异常过滤器
│   │   └── http-exception.filter.ts
│   ├── guards/                # 路由守卫
│   ├── interceptors/          # 拦截器
│   │   └── response.interceptor.ts
│   ├── loggers/               # 日志服务
│   │   └── daily-logger.service.ts
│   ├── middlewares/           # 中间件
│   │   └── request-logger.middleware.ts
│   ├── modules/               # 业务模块
│   │   └── auth/              # 认证授权模块
│   │       ├── components/    # 子组件
│   │       │   ├── users/     # 用户管理
│   │       │   ├── roles/     # 角色管理
│   │       │   ├── permissions/ # 权限管理
│   │       │   ├── depts/     # 部门管理
│   │       │   └── menus/     # 菜单管理
│   │       ├── guards/        # 认证守卫
│   │       ├── dto/           # 数据传输对象
│   │       ├── auth.controller.ts
│   │       ├── auth.service.ts
│   │       └── auth.module.ts
│   └── pipes/                 # 管道
├── config/                    # 配置文件
│   ├── env/                   # 环境变量
│   └── typeorm.config.ts      # 数据库配置
├── migrations/                # 数据库迁移文件
├── app.controller.ts          # 应用控制器
├── app.module.ts              # 应用模块
├── app.service.ts             # 应用服务
└── main.ts                    # 应用入口
```

## 🔧 开发指南

### 可用脚本

```bash
# 开发
pnpm run dev              # 开发模式启动（热重载）
pnpm run start            # 普通启动
pnpm run debug            # 调试模式启动

# 构建
pnpm run build            # 构建生产版本

# 代码质量
pnpm run lint             # ESLint 检查
pnpm run format           # Prettier 格式化

# 测试
pnpm run test             # 运行单元测试
pnpm run test:watch       # 监听模式运行测试
pnpm run test:cov         # 生成测试覆盖率报告
pnpm run test:e2e         # 运行端到端测试

# 数据库迁移
pnpm run migration:generate  # 生成迁移文件
pnpm run migration:create    # 创建空迁移文件
pnpm run migration:run       # 执行迁移
pnpm run migration:revert    # 回滚迁移
```

### 环境变量配置

项目支持多环境配置，在 `src/config/env/` 目录下创建对应环境的配置文件：

#### 开发环境 (.env.development)

```env
# 应用配置
NODE_ENV=development
PORT=3000

# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=nestjs_scaffold_dev

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1d

# 日志配置
LOG_LEVEL=debug
```

#### 生产环境 (.env.production)

```env
# 应用配置
NODE_ENV=production
PORT=3000

# 数据库配置
DATABASE_HOST=your-prod-host
DATABASE_PORT=5432
DATABASE_USER=your-prod-user
DATABASE_PASSWORD=your-prod-password
DATABASE_NAME=your-prod-database

# JWT 配置
JWT_SECRET=your-production-jwt-secret
JWT_EXPIRES_IN=1d

# 日志配置
LOG_LEVEL=info
```

## 🏗️ 架构设计

### 核心模块

#### 1. 认证授权模块 (Auth Module)

基于 RBAC (Role-Based Access Control) 模型设计：

- **用户 (Users)**: 系统用户管理
- **角色 (Roles)**: 角色定义和管理
- **权限 (Permissions)**: 细粒度权限控制
- **部门 (Departments)**: 组织架构管理
- **菜单 (Menus)**: 前端菜单权限控制

#### 2. 全局组件

- **异常过滤器**: 统一异常处理和错误响应格式
- **响应拦截器**: 统一成功响应格式
- **认证守卫**: JWT 令牌验证
- **权限守卫**: 基于角色和权限的访问控制
- **日志中间件**: 请求日志记录

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "data": {
    // 实际返回数据
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "statusCode": 400,
  "timestamp": "2025-06-23T10:30:00.000Z",
  "path": "/api/users",
  "message": "错误信息描述"
}
```

### 实体关系

```text
User (用户)
├── roles (多对多) → Role (角色)
├── department (多对一) → Department (部门)
└── createdBy/updatedBy → User

Role (角色)
├── users (多对多) → User (用户)
├── permissions (多对多) → Permission (权限)
└── menus (多对多) → Menu (菜单)

Permission (权限)
└── roles (多对多) → Role (角色)

Department (部门)
├── users (一对多) → User (用户)
├── parent (多对一) → Department (父部门)
└── children (一对多) → Department (子部门)

Menu (菜单)
├── roles (多对多) → Role (角色)
├── parent (多对一) → Menu (父菜单)
└── children (一对多) → Menu (子菜单)
```

## 📚 API 文档

启动应用后，访问 `http://localhost:3000/api` 查看完整的 Swagger API 文档。

### 主要 API 端点

#### 认证相关

- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/refresh` - 刷新令牌
- `POST /auth/logout` - 用户登出

#### 用户管理

- `GET /users` - 获取用户列表
- `GET /users/:id` - 获取用户详情
- `POST /users` - 创建用户
- `PUT /users/:id` - 更新用户
- `DELETE /users/:id` - 删除用户

#### 角色管理

- `GET /roles` - 获取角色列表
- `POST /roles` - 创建角色
- `PUT /roles/:id` - 更新角色
- `DELETE /roles/:id` - 删除角色

#### 权限管理

- `GET /permissions` - 获取权限列表
- `POST /permissions` - 创建权限
- `PUT /permissions/:id` - 更新权限
- `DELETE /permissions/:id` - 删除权限

#### 部门管理

- `GET /depts` - 获取部门列表
- `POST /depts` - 创建部门
- `PUT /depts/:id` - 更新部门
- `DELETE /depts/:id` - 删除部门

#### 菜单管理

- `GET /menus` - 获取菜单列表
- `POST /menus` - 创建菜单
- `PUT /menus/:id` - 更新菜单
- `DELETE /menus/:id` - 删除菜单

## 🗄️ 数据库

### 数据库迁移

项目使用 TypeORM 进行数据库管理，支持版本化的数据库迁移。

#### 创建迁移

```bash
# 根据实体变化自动生成迁移文件
pnpm run migration:generate

# 手动创建空迁移文件
pnpm run migration:create
```

#### 执行迁移

```bash
# 执行所有待执行的迁移
pnpm run migration:run

# 回滚最后一次迁移
pnpm run migration:revert
```

### 数据库连接配置

数据库配置位于 `src/config/typeorm.config.ts`，支持：

- 自动加载实体
- 数据库迁移管理
- 连接池配置
- 日志记录

## 🧪 测试

### 运行测试

```bash
# 运行所有单元测试
pnpm run test

# 监听模式运行测试
pnpm run test:watch

# 生成测试覆盖率报告
pnpm run test:cov

# 运行端到端测试
pnpm run test:e2e
```

### 测试结构

```text
test/
├── app.e2e-spec.ts          # 端到端测试
└── jest-e2e.json            # E2E 测试配置

src/
├── **/*.spec.ts             # 单元测试文件
└── **/*.controller.spec.ts  # 控制器测试文件
```

### 编写测试

#### 单元测试示例

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';

describe('UsersService', () => {
  let service: UsersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UsersService],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
```

## 📝 日志系统

### 日志配置

项目使用 Winston 进行日志管理，支持：

- **控制台输出**: 开发环境实时查看
- **文件轮转**: 按日期自动轮转日志文件
- **日志压缩**: 自动压缩历史日志
- **日志清理**: 自动清理过期日志

### 日志级别

- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息
- `verbose`: 详细信息

### 使用日志

```typescript
import { DailyLogger } from './common/loggers/daily-logger.service';

@Injectable()
export class SomeService {
  constructor(private readonly logger: DailyLogger) {}

  someMethod() {
    this.logger.log('这是一条信息日志');
    this.logger.error('这是一条错误日志', '错误堆栈');
    this.logger.warn('这是一条警告日志');
    this.logger.debug('这是一条调试日志');
  }
}
```

### 日志文件

日志文件存储在 `logs/` 目录下：

- `application-YYYY-MM-DD.log`: 按日期轮转的日志文件
- 自动压缩超过 20MB 的日志文件
- 保留最近 14 天的日志文件

## 🚀 部署

### 本地部署

```bash
# 构建项目
pnpm run build

# 启动生产服务
pnpm run prod
```

### Docker 部署

#### 1. 创建 Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package 文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["pnpm", "run", "prod"]
```

#### 2. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=nestjs_scaffold
    depends_on:
      - postgres
    volumes:
      - ./logs:/app/logs

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=nestjs_scaffold
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'

volumes:
  postgres_data:
```

#### 3. 部署步骤

```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

## 🔒 安全最佳实践

### 1. 环境变量安全

- 不要在代码中硬编码敏感信息
- 使用强密码和复杂的 JWT 密钥
- 定期轮换密钥和密码
- 生产环境使用环境变量管理敏感配置

### 2. 数据库安全

- 使用参数化查询防止 SQL 注入
- 限制数据库用户权限
- 定期备份数据库
- 启用数据库连接加密

### 3. API 安全

- 实施速率限制防止暴力攻击
- 使用 HTTPS 加密传输
- 验证和清理所有输入数据
- 实施适当的 CORS 策略
- 添加安全头部

### 4. 认证安全

- 使用强密码策略
- 实施账户锁定机制
- 记录和监控认证事件
- JWT 令牌设置合理的过期时间

## 🛠️ 开发最佳实践

### 代码规范

- 遵循 ESLint 和 Prettier 配置
- 使用 TypeScript 严格模式
- 编写有意义的注释
- 保持函数和类的单一职责

### 测试规范

- 为所有业务逻辑编写单元测试
- 为关键 API 编写集成测试
- 保持测试覆盖率在 80% 以上
- 使用描述性的测试名称

### Git 提交规范

```text
type(scope): description

[optional body]

[optional footer]
```

类型 (type):

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 性能优化

- 使用数据库索引优化查询
- 实施适当的缓存策略
- 优化 API 响应时间
- 监控应用性能指标

## 🤝 贡献指南

### 开发流程

1. Fork 项目到您的 GitHub 账户
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码审查

- 确保所有测试通过
- 遵循项目代码规范
- 更新相关文档
- 添加必要的测试用例

### 问题报告

如果您发现 bug 或有功能建议，请：

1. 检查是否已有相关 issue
2. 使用 issue 模板创建新问题
3. 提供详细的重现步骤
4. 包含相关的错误信息和日志

## 📚 学习资源

### 官方文档

- [NestJS 官方文档](https://docs.nestjs.com/)
- [TypeORM 文档](https://typeorm.io/)
- [PostgreSQL 文档](https://www.postgresql.org/docs/)
- [Winston 日志库](https://github.com/winstonjs/winston)
- [Jest 测试框架](https://jestjs.io/)

### 推荐阅读

- [NestJS 最佳实践](https://docs.nestjs.com/fundamentals/testing)
- [TypeScript 深入理解](https://www.typescriptlang.org/docs/)
- [Node.js 性能优化](https://nodejs.org/en/docs/guides/simple-profiling/)
- [PostgreSQL 性能调优](https://wiki.postgresql.org/wiki/Performance_Optimization)

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查数据库服务状态
pg_isready -h localhost -p 5432

# 检查环境变量配置
echo $DATABASE_HOST
```

#### 2. 迁移执行失败

```bash
# 检查迁移状态
pnpm run migration:show

# 手动回滚并重新执行
pnpm run migration:revert
pnpm run migration:run
```

#### 3. JWT 令牌验证失败

- 检查 JWT_SECRET 配置是否正确
- 确认令牌未过期
- 验证请求头格式：`Authorization: Bearer <token>`

#### 4. 端口占用问题

```bash
# 查看端口占用
netstat -tulpn | grep :3000

# 杀死占用进程
kill -9 <PID>
```

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证。

## 🙋‍♂️ 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 搜索已有的 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new) 并提供详细信息

## 🔗 相关链接

- [项目仓库](../../)
- [问题反馈](../../issues)
- [功能请求](../../issues/new?template=feature_request.md)
- [更新日志](CHANGELOG.md)

---

<p align="center">
  Made with ❤️ by NestJS Scaffold Team
</p>
