import {
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Permission } from '../../permissions/entities/permission.entity';
import { Dept } from '../../depts/entities/dept.entity';
import { Menu } from '../../menus/entities/menu.entity';

@Entity()
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    unique: true,
    length: 20,
  })
  code: string;

  @Column({
    length: 50,
  })
  name: string;

  @Column({
    length: 255,
    nullable: true,
  })
  remark: string;

  @Column({ type: 'enum', enum: [0, 1] })
  status: 0 | 1; // 状态 1 启用 0 停用

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;

  @ManyToMany(() => Permission)
  @JoinTable({
    name: 'role_permission_relation',
  })
  permissions: Permission[];

  @ManyToOne(() => Dept, (dept) => dept.roles)
  dept: Dept; // 部门

  @ManyToMany(() => Menu, (menu) => menu.roles)
  @JoinTable({
    name: 'role_menu_relation', // 连接表名称
    joinColumn: {
      name: 'role_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'menu_id',
      referencedColumnName: 'id',
    },
  })
  menus: Menu[];
}
