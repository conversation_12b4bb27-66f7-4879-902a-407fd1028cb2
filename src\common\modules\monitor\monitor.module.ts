import { Module } from '@nestjs/common';
import { MonitorService } from './monitor.service';
import { MonitorController } from './monitor.controller';
import { OnlineLogsModule } from './components/online-logs/online-logs.module';

@Module({
  controllers: [MonitorController],
  providers: [MonitorService],
  imports: [OnlineLogsModule],
  exports: [OnlineLogsModule, MonitorService],
})
export class MonitorModule {}
