import {
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Dept } from '../../depts/entities/dept.entity';
import { Role } from '../../roles/entities/role.entity';

export enum Gender {
  Female = 0,
  Male = 1,
}

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    unique: true,
  })
  username: string; // 用户名

  @Column({
    length: 50,
  })
  password: string; // 密码

  @Column({
    unique: true,
  })
  nickname: string; // 昵称

  @Column({ nullable: true })
  avatar: string; // 头像

  @Column({ nullable: true })
  email: string; // 邮箱

  @Column({ type: 'enum', enum: [0, 1] })
  status: 0 | 1; // 状态 1 启用 0 停用

  @Column({
    nullable: true,
    unique: true,
    length: 11,
  })
  phone: string; // 手机号

  @Column({ type: 'enum', enum: Gender, enumName: 'sex' })
  sex: Gender; // 性别

  @Column({ nullable: true })
  remark: string; // 备注

  @CreateDateColumn()
  createTime: Date; // 创建时间

  @UpdateDateColumn()
  updateTime: Date; // 更新时间

  // JoinTable 表示多对多关系的关联表的配置
  @ManyToMany(() => Role)
  @JoinTable({
    name: 'user_role_relation',
  })
  roles: Role[];

  // JoinTable 表示多对多关系的关联表的配置
  @ManyToOne(() => Dept, (dept) => dept.roles)
  dept: Dept; // 部门
}
