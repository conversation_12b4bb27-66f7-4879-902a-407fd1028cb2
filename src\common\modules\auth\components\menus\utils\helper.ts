import { Menu } from '../entities/menu.entity';

/**
 * 将数据库格式转换为前端路由格式
 * @param menus
 */
export function convertTheDataFormat(menus: Menu[]) {
  const formatMenu = (menu: Menu) => {
    let returnDate = {
      path: menu.path,
      name: menu.name,
      // component: menu.component,
      meta: {
        icon: menu.icon,
        title: menu.title,
        rank: menu.rank,
        roles: menu.roles?.map((role) => role.code) ?? [],
        // component: menu.component,
        // keepAlive: menu.keepAlive,
        // showLink: menu.showLink,
        // 其他需要映射的meta字段
      },
      children: [],
    };

    // 如果 rank 为0，则删除 rank 字段
    if (returnDate.meta.rank === 0) {
      delete returnDate.meta.rank;
    }

    return returnDate;
  };

  // 构建菜单树
  const menuMap = new Map();
  const rootMenus = [];

  // 第一遍遍历：创建所有菜单的映射
  menus.forEach((menu) => {
    const formattedMenu = formatMenu(menu);
    menuMap.set(menu.id, formattedMenu);
    if (!menu.parent) {
      rootMenus.push(formattedMenu);
    }
  });

  // 第二遍遍历：构建层级关系
  menus.forEach((menu) => {
    if (menu.parent) {
      const parentMenu = menuMap.get(menu.parent.id);
      if (parentMenu) {
        let children = menuMap.get(menu.id) as Menu;

        if (children.children.length === 0) {
          delete children.children;
        }

        parentMenu.children.push(children);
      }
    }
  });

  return rootMenus;
}
