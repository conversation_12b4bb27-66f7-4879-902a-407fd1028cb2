import {
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  MinLength,
  Length,
  Matches,
  IsEnum,
  IsOptional,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../../roles/entities/role.entity';
import { Gender, User } from '../entities/user.entity';

export class CreateUserDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    description: '昵称',
    example: '管理员',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  nickname: string;

  @ApiProperty({
    description: '密码',
    example: '123456',
    minLength: 6,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: '父级用户ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  parentId: number;

  @ApiProperty({
    description: '手机号码',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  phone: string;

  @ApiProperty({
    description: '邮箱地址',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  email: string;

  @ApiProperty({
    description: '用户状态',
    example: 1,
    enum: [0, 1],
    required: false,
  })
  @IsOptional()
  status: 0 | 1;

  @ApiProperty({
    description: '性别',
    example: 1,
    enum: Gender,
    enumName: 'Gender',
    required: false,
  })
  @IsOptional()
  @IsEnum(Gender, { message: 'Sex must be either 0 or 1' })
  sex: Gender;

  @ApiProperty({
    description: '备注',
    example: '系统管理员',
    required: false,
  })
  @IsOptional()
  remark: string;

  @ApiProperty({
    description: '角色列表',
    type: [Role],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => Role)
  roles: Role[] = [];
}

export class LoginUserDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    minLength: 1,
    maxLength: 50,
    required: true,
  })
  @IsNotEmpty()
  @Length(1, 50)
  username: string;

  @ApiProperty({
    description: '密码',
    example: '123456',
    minLength: 1,
    maxLength: 50,
    required: true,
  })
  @IsNotEmpty()
  @Length(1, 50)
  password: string;
}

export class SearchUserDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    required: false,
  })
  @IsString()
  @IsOptional()
  username: string;

  @ApiProperty({
    description: '用户状态',
    example: 1,
    enum: [0, 1],
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsEnum([0, 1], { message: 'Status must be either 0 or 1' })
  status: 0 | 1;

  @ApiProperty({
    description: '手机号码',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone: string;

  @ApiProperty({
    description: '部门ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  deptId: number;
}
