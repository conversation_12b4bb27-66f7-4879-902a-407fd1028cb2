import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { DeptsService } from './depts.service';
import { CreateDeptDto, SearchDeptDto } from './dto/create-dept.dto';
import { UpdateDeptDto } from './dto/update-dept.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

@ApiTags('depts(部门)')
@Controller('depts')
export class DeptsController {
  constructor(private readonly deptsService: DeptsService) {}

  @Post('/create')
  @ApiOperation({
    summary: '创建部门',
    description: '创建一个新的部门',
  })
  @ApiBody({ type: CreateDeptDto })
  @ApiResponse({
    status: 201,
    description: '部门创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 403,
    description: '没有权限',
  })
  create(@Body() createDeptDto: CreateDeptDto) {
    return this.deptsService.create(createDeptDto);
  }

  @Post('/search')
  @ApiOperation({
    summary: '查询部门',
    description: '根据条件查询部门信息',
  })
  @ApiBody({ type: SearchDeptDto })
  @ApiResponse({
    status: 200,
    description: '成功获取部门列表',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async findByCondition(@Body() searchDeptDto: SearchDeptDto) {
    // 判断是否为空或者空对象
    if (searchDeptDto == null || Object.keys(searchDeptDto).length === 0) {
      return await this.deptsService.findAll();
    }
    return await this.deptsService.findByCondition(searchDeptDto);
  }

  @Patch('update/:id')
  @ApiOperation({
    summary: '更新部门',
    description: '根据ID更新部门信息',
  })
  @ApiParam({
    name: 'id',
    description: '部门ID',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdateDeptDto })
  @ApiResponse({
    status: 200,
    description: '部门更新成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 404,
    description: '部门不存在',
  })
  update(@Param('id') id: string, @Body() updateDeptDto: UpdateDeptDto) {
    return this.deptsService.update(+id, updateDeptDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: '删除部门',
    description: '根据ID删除部门',
  })
  @ApiParam({
    name: 'id',
    description: '部门ID',
    type: Number,
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: '部门删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '部门不存在',
  })
  remove(@Param('id') id: string) {
    return this.deptsService.remove(+id);
  }
}
