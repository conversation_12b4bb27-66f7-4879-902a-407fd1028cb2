import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
  ValidateNested,
} from 'class-validator';
import { Role } from '../entities/role.entity';
import { Transform, Type } from 'class-transformer';
import { Permission } from '../../permissions/entities/permission.entity';

export class CreateRoleDto {}

export class SearchRoleDto {
  @IsString()
  @IsOptional()
  code: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsEnum([0, 1], { message: '状态必须是0或1' })
  status: 0 | 1;

  @IsOptional()
  @IsString()
  remark: string;
}
