import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from 'typeorm';

@Entity('online_logs')
export class OnlineLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  username: string;

  @Column()
  ip: string;

  @Column()
  address: string;

  @Column()
  system: string;

  @Column()
  browser: string;

  @CreateDateColumn({ name: 'login_time' })
  loginTime: Date;
}
