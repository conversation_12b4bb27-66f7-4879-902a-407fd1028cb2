import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { Role } from './entities/role.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from '../users/users.module';
import { Menu } from '../menus/entities/menu.entity';
import { MenusService } from '../menus/menus.service';
import { MenusModule } from '../menus/menus.module';

@Module({
  imports: [TypeOrmModule.forFeature([Role]), MenusModule],
  controllers: [RolesController],
  providers: [RolesService],
  exports: [TypeOrmModule, RolesService], // 导出 TypeOrmModule 以便其他模块使用
})
export class RolesModule {}
