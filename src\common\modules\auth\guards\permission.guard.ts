import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { UsersService } from '../components/users/users.service';
import { Permission } from '../components/permissions/entities/permission.entity';
import { Reflector } from '@nestjs/core';
import { RolesService } from '../components/roles/roles.service';

@Injectable()
export class PermissionGuard implements CanActivate {
  // 注入 UsersService，需要在 module 中导入 UsersModule
  @Inject(RolesService)
  private rolesService: RolesService;

  @Inject('Reflector')
  private reflector: Reflector;

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: Request = context.switchToHttp().getRequest();

    // 确定是否登录
    if (!request.user) {
      return false;
    }

    // 获取用户的角色
    const roles = await this.rolesService.findRolesByIds(
      request.user.roles.map((item) => item.id),
    );

    // 获取用户的权限
    const permissions: Permission[] = roles.reduce((total, current) => {
      total.push(...current.permissions);
      return total;
    }, []);

    // 获取当前接口需要的权限名称
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      'require-permission',
      [context.getClass(), context.getHandler()],
    );

    // 判断是否有权限
    for (let i = 0; i < requiredPermissions.length; i++) {
      const curPermission = requiredPermissions[i];
      const found = permissions.find((item) => item.name === curPermission);
      if (!found) {
        throw new UnauthorizedException('您没有访问该接口的权限');
      }
    }

    return true;
  }
}
