import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import {
  CreateUserDto,
  LoginUserDto,
  SearchUserDto,
} from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtService } from '@nestjs/jwt';
import { NotRequireLogin } from '../common/decorators/not-require-login.decorator';
import { response } from 'express';
import { Role } from '../roles/entities/role.entity';
import { RolesService } from '../roles/roles.service';

@ApiTags('users(用户)')
@Controller('users')
@ApiBearerAuth() // 开启 BearerAuth 授权认证
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
  ) {}

  @Post('/create')
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 403, description: '没有权限' })
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get('/all')
  @ApiOperation({
    summary: '查询所有用户',
    description: '获取系统中所有用户的列表',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取用户列表',
    type: [CreateUserDto],
  })
  findAll() {
    return this.usersService.findAll();
  }

  @Post('/search')
  @ApiOperation({
    summary: '根据条件查询用户',
    description: '根据指定的条件筛选用户',
  })
  @ApiBody({ type: SearchUserDto })
  @ApiResponse({
    status: 200,
    description: '成功获取符合条件的用户列表',
    type: [CreateUserDto],
  })
  async findByCondition(@Body() searchUserDto: SearchUserDto) {
    // 判断是否为空或者空对象
    return await this.usersService.findByCondition(searchUserDto);
  }

  @Patch('update/:id')
  @ApiOperation({
    summary: '更新用户',
    description: '根据用户ID更新用户信息',
  })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: '用户信息更新成功',
    type: CreateUserDto,
  })
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(+id, updateUserDto);
  }

  // @Delete(':id')
  // @ApiOperation({ summary: '删除用户' })
  // remove(@Param('id') id: string) {
  //   return this.usersService.remove(+id);
  // }

  // 批量删除
  @Delete('/delete')
  @ApiOperation({
    summary: '批量删除用户',
    description: '根据用户ID列表批量删除用户',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        ids: {
          type: 'array',
          items: { type: 'number' },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '用户删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '部分用户不存在',
  })
  async delete(@Body() data: { ids: number[] }) {
    return await this.usersService.delete(data.ids);
  }

  // 重置密码
  @Post('/reset-password/:id')
  @ApiOperation({
    summary: '重置密码',
    description: '重置指定用户的密码',
  })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        password: {
          type: 'string',
          description: '新密码',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '密码重置成功',
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async resetPassword(
    @Param('id') id: string,
    @Body() data: { password: string },
  ) {
    return await this.usersService.update(+id, { password: data.password });
  }

  // 根据用户id查询角色
  @Post('/list-role-ids')
  @ApiOperation({
    summary: '根据用户id查询角色',
    description: '获取指定用户关联的角色ID列表',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        userId: {
          type: 'number',
          description: '用户ID',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '成功获取角色ID列表',
    schema: {
      type: 'array',
      items: { type: 'number' },
    },
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async GetRoleUserIds(@Body() data: { userId: number }) {
    // 根据角色id查询角色的菜单
    const user = await this.usersService.findOne(data.userId);

    // 返回角色的菜单id
    if (user === undefined) {
      throw new NotFoundException('用户不存在');
    } else {
      return user.roles.map((role) => role.id);
    }
  }

  // 分配角色
  @Post('/assign-role/:id')
  @ApiOperation({
    summary: '分配角色',
    description: '为用户分配角色',
  })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        roleIds: {
          type: 'array',
          items: { type: 'number' },
          description: '角色ID列表',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '角色分配成功',
    type: CreateUserDto,
  })
  @ApiResponse({
    status: 404,
    description: '用户或角色不存在',
  })
  async assignRole(
    @Param('id') id: string,
    @Body() data: { roleIds: number[] },
  ) {
    const roles = await this.rolesService.findRolesByIds(data.roleIds);
    return await this.usersService.update(+id, { roles: roles });
  }
}
