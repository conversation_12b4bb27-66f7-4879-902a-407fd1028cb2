import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  ManyToOne,
  OneToMany,
  JoinTable,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Role } from '../../roles/entities/role.entity';

@Entity()
export class Menu {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: 0 })
  menuType: number; // 菜单类型（0代表菜单、1代表iframe、2代表外链、3代表按钮）

  @Column({ length: 100 })
  title: string; // 菜单名称

  @Column({ length: 100 })
  name: string; // 路由名称

  @Column({ length: 200 })
  path: string; // 路由路径

  @Column({ nullable: true, length: 200 })
  component: string; // 组件路径

  @Column({ nullable: true })
  rank: number; //菜单排序, 可以为空

  @Column({ nullable: true, length: 200 })
  redirect: string; // 路由重定向

  @Column({ nullable: true, length: 100 })
  icon: string; // 菜单图标

  @Column({ nullable: true, length: 100 })
  extraIcon: string; // 右侧图标

  @Column({ nullable: true, length: 100 })
  enterTransition: string; // 进场动画

  @Column({ nullable: true, length: 100 })
  leaveTransition: string; // 离场动画

  @Column({ nullable: true, length: 200 })
  activePath: string; // 菜单激活

  @Column({ nullable: true, length: 200 })
  auths: string;

  @Column({ nullable: true, length: 200 })
  frameSrc: string;

  @Column({ default: true })
  frameLoading: boolean;

  @Column({ default: false })
  keepAlive: boolean; // 是否缓存页面

  @Column({ default: false })
  hiddenTag: boolean; // 是否隐藏标签

  @Column({ default: false })
  fixedTag: boolean; // 是否固定标签

  @Column({ default: true })
  showLink: boolean;

  @Column({ default: false })
  showParent: boolean; // 是否显示父级

  @ManyToOne(() => Menu, (menu) => menu.children)
  parent: Menu;

  @OneToMany(() => Menu, (menu) => menu.parent)
  children: Menu[];

  @ManyToMany(() => Role, (role) => role.menus)
  @JoinTable({
    name: 'role_menu_relation', // 连接表名称
    joinColumn: {
      name: 'menu_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'role_id',
      referencedColumnName: 'id',
    },
  })
  roles: Role[];

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}
