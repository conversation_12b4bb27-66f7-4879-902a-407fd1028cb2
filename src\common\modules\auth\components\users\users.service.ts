import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import {
  CreateUserDto,
  LoginUserDto,
  SearchUserDto,
} from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { EntityManager, In, Like, Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Role } from '../roles/entities/role.entity';
import { Permission } from '../permissions/entities/permission.entity';
import { Dept } from '../depts/entities/dept.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * 根据条件查询用户
   */
  async findByCondition(
    searchUserDto: SearchUserDto,
    page: number = 1,
    limit: number = 10,
  ) {
    const { deptId, username, phone, ...otherConditions } = searchUserDto;

    const queryBuilder = this.entityManager
      .createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.roles', 'roles')
      .leftJoinAndSelect('roles.permissions', 'permissions')
      .leftJoinAndSelect('user.dept', 'dept')
      .where(otherConditions);

    // 这部分后面可以类似的方式进行封装，提取成一个函数
    if (deptId) {
      queryBuilder.andWhere('dept.id = :deptId', {
        deptId: deptId,
      });
    }

    if (username) {
      queryBuilder.andWhere('user.username LIKE :username', {
        username: `%${username}%`,
      });
    }

    if (phone) {
      queryBuilder.andWhere('user.phone LIKE :phone', {
        phone: `%${phone}%`,
      });
    }

    queryBuilder.skip((page - 1) * limit).take(limit);

    const [users, total] = await queryBuilder.getManyAndCount();

    return {
      list: users,
      total,
      currentPage: page,
      pageSize: limit,
    };
  }

  /**
   * 用户登录
   * @param loginUserDto 用户登录信息
   * @returns
   */
  async login(loginUserDto: LoginUserDto) {
    const user = await this.entityManager.findOne(User, {
      where: {
        username: loginUserDto.username,
      },
      // 返回的数据中包含 roles 和 roles.permissions 两个外键关联的数据
      relations: ['roles', 'roles.permissions'],
    });

    if (!user) {
      throw new HttpException('用户不存在', HttpStatus.ACCEPTED);
    }

    if (user.password !== loginUserDto.password) {
      throw new HttpException('密码错误', HttpStatus.ACCEPTED);
    }

    return user;
  }

  /**
   * 创建用户
   * @param createUserDto
   * @returns
   */
  async create(createUserDto: CreateUserDto) {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  /**
   * 查询所有用户
   * @returns
   */
  async findAll() {
    return this.userRepository.find({
      relations: ['roles', 'roles.permissions', 'dept'],
    });
  }

  /**
   * 查询指定用户
   * @param id
   * @returns
   */
  async findOne(id: number) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['roles', 'roles.permissions', 'dept'],
    });
    if (!user) {
      throw new NotFoundException(`没有找到ID为 ${id} 的用户`);
    }
    return user;
  }

  /**
   * 更新用户
   * @param updateUserDto
   * @returns
   */
  async update(id: number, updateUserDto: UpdateUserDto) {
    // 根据 parentId 查询部门
    let dept: Dept = undefined;
    if (updateUserDto.parentId) {
      dept = await this.entityManager.findOne(Dept, {
        where: { id: updateUserDto.parentId },
      });
      if (!dept) {
        throw new NotFoundException(
          `没有找到ID为 ${updateUserDto.parentId} 的部门`,
        );
      }
    }

    const user = await this.userRepository.preload({
      id: id,
      dept: dept,
      ...updateUserDto,
    });
    if (!user) {
      throw new NotFoundException(`没有找到ID为 ${id} 的权限`);
    }
    return this.userRepository.save(user);
  }

  /**
   * 删除用户
   * @param id
   */
  async remove(id: number) {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }

  /**
   * 批量删除用户
   * @param ids
   */
  async delete(ids: number[]) {
    const users = await this.userRepository.find({
      where: {
        id: In(ids),
      },
    });
    await this.userRepository.remove(users);
  }
}
