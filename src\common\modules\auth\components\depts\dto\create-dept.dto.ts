import {
  <PERSON><PERSON>tring,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  <PERSON><PERSON><PERSON><PERSON>,
  Length,
  Matches,
  IsEnum,
  IsOptional,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../../roles/entities/role.entity';
import { Gender, User } from '../../users/entities/user.entity';
import { CreateRoleDto } from '../../roles/dto/create-role.dto';

export class CreateDeptDto {
  @ApiProperty({
    description: '部门ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  id: number;

  @ApiProperty({
    description: '上级部门选项',
    type: [CreateDeptDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDeptDto)
  higherDeptOptions: CreateDeptDto[];

  @ApiProperty({
    description: '关联角色',
    type: [CreateRoleDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @Type(() => CreateRoleDto)
  roles: CreateRoleDto[];

  @ApiProperty({
    description: '子部门',
    type: [CreateDeptDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @Type(() => CreateDeptDto)
  children: CreateDeptDto[];

  @ApiProperty({
    description: '父部门ID',
    example: 0,
    required: false,
  })
  @IsOptional()
  parentId: number;

  @ApiProperty({
    description: '部门名称',
    example: '研发部',
    required: false,
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: '部门负责人',
    example: '张三',
    required: false,
  })
  @IsString()
  @IsOptional()
  principal: string;

  @ApiProperty({
    description: '联系电话',
    example: '13800138000',
    required: false,
  })
  @IsString()
  @IsOptional()
  phone: string;

  @ApiProperty({
    description: '邮箱',
    example: '<EMAIL>',
    required: false,
  })
  @IsString()
  @IsOptional()
  email: string;

  @ApiProperty({
    description: '排序',
    example: 1,
    required: false,
  })
  @IsOptional()
  sort: number;

  @ApiProperty({
    description: '状态：0-禁用，1-启用',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsEnum([0, 1], { message: 'Status must be either 0 or 1' })
  status: 0 | 1;

  @ApiProperty({
    description: '备注',
    example: '这是研发部门',
    required: false,
  })
  @IsString()
  @IsOptional()
  remark: string;
}

export class SearchDeptDto {
  @ApiProperty({
    description: '部门ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  id: number;

  @ApiProperty({
    description: '部门名称',
    example: '研发部',
    required: false,
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: '部门别名',
    example: 'R&D',
    required: false,
  })
  @IsString()
  @IsOptional()
  nickname: string;

  @ApiProperty({
    description: '父部门ID',
    example: 0,
    required: false,
  })
  @IsOptional()
  parentId: number;

  @ApiProperty({
    description: '部门负责人',
    example: '张三',
    required: false,
  })
  @IsString()
  @IsOptional()
  principal: string;

  @ApiProperty({
    description: '备注',
    example: '这是研发部门',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark: string;

  @ApiProperty({
    description: '角色ID列表',
    example: [1, 2],
    required: false,
  })
  @IsOptional()
  @IsArray()
  roleId: number[];

  @ApiProperty({
    description: '子部门ID',
    example: 2,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  childrenId: number;
}
